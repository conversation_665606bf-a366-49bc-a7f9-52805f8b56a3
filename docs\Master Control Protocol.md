将整个流程用“主控提示（MCP）”的思想来实现，意味着我们将整个开发工作流看作是一系列由AI代理执行的、结构化的、相互关联的“提示链（Prompt Chaining）”。

这里的“MCP”不再仅仅是项目启动时那份静态的《主控提示模板》，而是扩展为一个**“主控提示协议（Master Control Protocol）”**——一个通过编排一系列专门的、上下文感知的提示来驱动整个开发生命周期的系统。

以下是如何设计与实现一个完全基于MCP协议的工具。

------

### 设计理念：从单一提示到AI代理的协同协议

我们将设计一个系统，其中包含多个**专门化（Specialized）的AI代理**，每个代理都由一个独特的、为其任务量身定制的“主控提示”来驱动。工具的核心工作就是**在正确的时间，用正确的上下文，调用正确的代理**。

### 实现方案：分阶段的MCP驱动流程

#### **第一阶段：项目初始化 - “根主控提示 (Root MCP)”**

这是对您提供的《主控提示模板》最直接的应用。

- **设计**：

  - 这一阶段使用一个**“根主控提示 (Root MCP)”**，即用户填写的那份 `master_prompt.md` 文件。
  - 我们设计一个名为**“架构师代理 (Architect Agent)”**的AI代理，它的唯一职责就是解析这份根MCP。

- **实现**：

  1. **用户输入**：技术负责人填写 `master_prompt.md`。

  2. **MCP调用**：工具将 `master_prompt.md` 的全部内容作为核心上下文，去调用“架构师代理”。

  3. **代理的内部提示（伪代码）**：

     ```
     SYSTEM PROMPT: 
     你是一位顶级的系统架构师和CTO。你的任务是基于用户提供的以下 "项目上下文"，生成一套完整的项目启动规约文件。
     
     [此处注入用户填写的 master_prompt.md 的全部内容]
     
     请严格按照此上下文，为我生成以下三个文件：
     1.  `product.md`：总结项目的核心价值和业务领域。
     2.  `architecture.md`：详细描述技术栈、架构原则和关键非功能性需求。
     3.  `conventions.md`：制定详细的编码规范、API设计指南和版本控制策略。
     ```

  4. **输出**：生成初始的 `.spec/` 目录和其中的核心规约文件。

#### **第二阶段：特性开发 - “特性主控提示链 (Feature MCP Chain)”**

这是MCP思想的延伸，将单次任务分解为一系列环环相扣的提示。这借鉴了Kiro分析中提到的“提示链（Prompt Chaining）”思想，它能显著提升AI在复杂任务上的表现。

- **设计**：

  - 为特性开发的每一步（需求、设计、任务）都设计一个专门的**“特性主控提示 (Feature MCP)”**。
  - 设计三个专门的代理：**“产品经理代理”**、**“设计师代理”和“规划师代理”**。
  - 最关键的设计是**动态上下文注入**：上一步的输出会自动成为下一步提示的核心输入。

- **实现**：

  1. **需求生成 (调用“产品经理代理”)**：

     - **用户输入**：`/spec "添加用户登录功能"`

     - **MCP调用**：工具自动抓取`.spec/product.md`的内容，并与用户输入组合成一个MCP。

     - **代理的内部提示**：

       ```
       SYSTEM PROMPT:
       你是一位产品经理。在一个产品愿景为 [此处注入 product.md 内容] 的项目中，开发者需要 "[添加用户登录功能]"。请将此意图转化为一份使用EARS语法的 `requirements.md` 文件。
       ```

  2. **设计生成 (调用“设计师代理”)**：

     - **用户输入**：在生成的`requirements.md`界面上点击“批准”。

     - **MCP调用**：工具自动抓取`architecture.md`, `conventions.md`, 已批准的`requirements.md`以及通过代码库分析找到的相关代码片段，将它们组合成一个新的MCP。

     - **代理的内部提示**：

       ```
       SYSTEM PROMPT:
       你是一位软件设计师。请遵循以下架构原则 [注入 architecture.md] 和编码规范 [注入 conventions.md]，为这份已批准的需求 [注入 requirements.md] 生成一份详细的 `design.md` 技术设计方案。
       ```

  3. **任务生成 (调用“规划师代理”)**：

     - **用户输入**：在生成的`design.md`界面上点击“批准”。

     - **MCP调用**：工具将已批准的`design.md`作为MCP的核心。

     - **代理的内部提示**：

       ```
       SYSTEM PROMPT:
       你是一位资深开发组长。请将这份技术设计方案 [注入 design.md] 分解为一份详细、可执行、有顺序的 `tasks.md` 列表。
       ```

#### **第三阶段：自动化治理 - “钩子主控提示 (Hook MCPs)”**

这是将自动化机制也用MCP来驱动的设计。

- **设计**：

  - 将每一个自动化钩子（Hook）都设计成一个由事件触发的、高度聚焦的**“钩子主控提示”**。
  - 设计多个“微代理”，如**“测试工程师代理”**、**“代码审查员代理”**等。

- **实现 (以“自动生成单元测试”为例)**：

  1. **事件触发**：开发者保存了 `src/api/auth.py` 文件。

  2. **MCP调用**：

     - 工具监听到“on-save”事件。
     - 它自动抓取被保存文件的**代码内容**、项目中相关的**验收标准**（从`requirements.md`中匹配）以及`conventions.md`中定义的**测试框架**（如pytest）。
     - 它将这些动态上下文组合成一个临时的“钩子MCP”。

  3. **代理的内部提示 (调用“测试工程师代理”)**：

     ```
     SYSTEM PROMPT:
     你是一位自动化测试工程师。请为以下Python代码 [注入 auth.py 的代码] 编写单元测试。这些测试必须覆盖以下验收标准 [注入相关的验收标准]。请使用 `pytest` 框架来编写。
     ```

  4. **输出**：AI代理生成或更新 `tests/test_auth.py` 文件，并以`diff`形式呈现给用户。

### 总结

通过这种设计，**“主控提示（MCP）”** 升华为一个驱动整个开发流程的协议：

- **它不再是静态的**，而是根据当前任务动态生成。
- **它是有状态的**，因为每一步的输出都成为下一步的输入，形成了记忆链。
- **它是高度结构化的**，通过编排不同的专业代理和注入精确的上下文，来确保AI在复杂的软件工程任务中保持专注、不偏离方向，最终实现高质量、规范化的软件交付。