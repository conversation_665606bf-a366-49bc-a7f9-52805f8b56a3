**构建一个由AI驱动的、覆盖软件开发全生命周期的“规约驱动开发（Spec-Driven Development）”治理框架。该框架旨在将项目的战略目标、架构原则和工程规范制度化，通过AI代理将高层级的业务需求系统性地转化为结构化的需求、设计和可执行任务，并在开发过程中通过自动化机制保障规范的落地执行，从而解决“氛围编码（vibe coding）”带来的技术债务问题，确保大规模、长周期项目的代码质量、可维护性和团队一致性。**

这个框架的实现可以分解为三个核心组成部分，形成一个“**策略定义 -> 合规规划 -> 监控执行**”的完整治理闭环。

------



### **第一部分：框架基础：项目宪法与代理引导 (Project Constitution & Agent Steering)**



这部分对应于“**策略定义**”，其目标是在项目启动之初，利用AI建立一套持久化的、作为项目唯一“真理之源”的顶层规则和上下文。这借鉴了Kiro的“Agent Steering”机制 和“通用工作流”中的“项目宪法”理念。

1. **输入源：主控提示（Master Control Prompt）**
   - 项目负责人（如CTO或架构师）需填写一份类似于您提供的《主控提示模板》的文档。
   - 这份文档将捕获项目的核心上下文，包括：
     - **项目概述**：核心价值、业务领域、项目阶段。
     - **技术栈**：明确的前后端语言、框架、数据库、基础设施等。
     - **团队情况**：规模、构成与经验水平。
     - **关键非功能性需求**：性能、安全性、合规性等硬性指标。
     - **核心设计原则与禁止项**：例如，必须遵循的编码规范（PEP 8）、必须使用的设计模式（Repository模式）、禁止使用的技术（ORM）等。
2. **AI生成的核心规约文档集**
   - 基于上述输入，AI代理将生成一套项目级的“宪法”文件，存放于专门目录（如 `.project-spec/`）中。
   - **`product.md`**：阐述产品愿景、目标用户和核心业务逻辑，为AI提供业务层面的上下文。
   - **`architecture.md`**：定义技术栈、核心架构原则、基础设施蓝图，并可包含由AI生成的架构图（如使用Mermaid.js语法）。
   - **`conventions.md`**：包含详细的编码规范、API设计指南、版本控制策略（如Git Flow）、命名约定等具体规则。

这些文件将作为AI代理在后续所有工作中必须遵守的“护栏”，确保其所有产出都符合项目的顶层设计。



### **第二部分：核心流程：规约驱动的特性开发 (Spec-Driven Feature Development)**



这部分对应于“**合规规划**”，它将Kiro的“spec”生成流程 应用于每一个新功能的开发，确保从需求到代码的每一步都有据可循。

1. **输入源：开发者的高层级意图**
   - 开发者用自然语言提出一个功能需求，例如：“我需要一个用户管理模块，包含注册、登录、获取用户信息功能”。
2. **AI驱动的三段式规约生成**
   - AI代理接收到意图后，并结合第一部分生成的“项目宪法”，启动一个转化漏斗，依次生成以下三个核心文件：
     1. **`requirements.md` (需求规约)**：
        - 将模糊的需求转化为结构化的用户故事（User Stories）和无歧义、可测试的验收标准（Acceptance Criteria）。
        - 可采用EARS（简易需求语法方法）等格式，明确功能范围和边界条件，这是功能的“真理之源”。
     2. **`design.md` (设计规约)**：
        - 基于需求和项目架构规范，AI分析现有代码库，提出详细的技术设计方案，充当“架构蓝图”。
        - 内容可包括：对现有架构的修改、组件接口定义（如TypeScript接口）、API端点设计、数据库模式变更等。
     3. **`tasks.md` (任务规约)**：
        - 将宏观设计分解为一系列原子化的、按顺序排列的编码任务，构成一份“可执行计划”。
        - **核心特性**：每个任务都必须能追溯到其满足的一项或多项需求 (`requirements.md`)，确保每一行代码的实现都有据可依。



### **第三部分：闭环保障：自动化测试与工作流执行 (Automated Testing & Workflow Enforcement)**



这部分对应于“**监控执行**”，旨在通过自动化手段，确保第二部分规划的规约在开发实践中得到有效遵守，并解决Kiro面临的“规约漂移”挑战。

1. **微循环开发模式**
   - 在执行每个具体任务时，遵循“**定义 -> 生成 -> 评审 -> 集成**”的微循环。开发者不再是代码的“打字员”，而是需求的“描述者”和AI生成代码的“评审员”。
2. **AI辅助测试**
   - 开发者可以选中一个函数或组件，让AI根据`requirements.md`中的验收标准，自动生成单元测试（如 `pytest`）和集成测试，覆盖所有主要分支和边界情况。
3. **自动化钩子（Agent Hooks）**
   - 建立一个类似Kiro的事件驱动自动化系统，在后台强制执行规约。
   - **示例场景**：
     - **代码质量**：在文件保存时，自动运行Linter和Formatter，检查是否符合`conventions.md`中的规范。
     - **测试自动化**：当一个功能文件被修改时，自动运行其对应的测试用例。
     - **文档同步**：当API代码发生变更时，自动更新项目的`README.md`或API文档，实现“活文档”。