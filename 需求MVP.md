# MCP规约驱动开发工具 - MVP需求文档

## 1. MVP产品概述

### 1.1 产品定位
一个基于MCP（Model Context Protocol）的轻量级规约驱动开发工具，专为个人开发者和小团队设计，通过AI辅助将自然语言需求转化为结构化的开发规约。

### 1.2 目标用户
- **主要用户**：个人开发者、SOLO founder、小型开发团队（1-5人）
- **使用场景**：个人项目、创业项目、快速原型开发

### 1.3 核心价值主张
- 快速将想法转化为结构化的开发计划
- 避免"氛围编程"，提供清晰的开发路径
- 轻量级工具，无需复杂的基础设施
- 本地优先，保护代码隐私

## 2. MVP功能范围

### 2.1 核心功能（必须实现）

#### 2.1.1 项目初始化
**功能描述**：快速创建项目规约框架

**用户故事**：
- 作为个人开发者，我希望能够快速初始化一个项目的基础规约，以便开始结构化开发

**实现方式**：
- 命令行工具：`mcp-spec init [project-name]`
- 生成基础文件结构：
  ```
  .spec/
  ├── project.md      # 项目概述
  ├── architecture.md # 技术架构
  └── conventions.md  # 开发约定
  ```

**验收标准**：
- 支持交互式项目信息收集
- 自动生成项目宪法文档模板
- 支持常见技术栈模板（React、Vue、Python、Node.js等）

#### 2.1.2 需求规约生成
**功能描述**：将自然语言需求转化为结构化规约

**用户故事**：
- 作为开发者，我希望输入功能描述后能生成详细的需求文档和任务列表

**实现方式**：
- 命令行工具：`mcp-spec generate "添加用户登录功能"`
- MCP服务器集成，调用AI模型生成规约
- 生成三个核心文件：
  - `requirements.md` - 需求规约
  - `design.md` - 设计规约  
  - `tasks.md` - 任务分解

**验收标准**：
- 支持中英文需求描述
- 生成符合EARS语法的验收标准
- 任务可追溯到具体需求
- 生成时间 < 30秒

#### 2.1.3 任务执行跟踪
**功能描述**：跟踪任务执行状态和进度

**用户故事**：
- 作为开发者，我希望能够标记任务完成状态，跟踪开发进度

**实现方式**：
- 命令行工具：`mcp-spec task list/complete/reset`
- 基于Markdown的任务状态管理
- 简单的进度统计和报告

**验收标准**：
- 支持任务状态切换（待办/进行中/已完成）
- 显示项目整体进度
- 支持任务筛选和搜索

#### 2.1.4 文档自动更新机制
**功能描述**：监控文档变更并自动更新相关规约

**用户故事**：
- 作为开发者，我希望修改项目宪法后，相关的功能规约能够自动更新以保持一致性
- 作为开发者，我希望更新需求文档后，设计和任务文档能够自动同步更新

**实现方式**：
- 文件监控：使用文件系统监控API检测文档变更
- 智能更新：分析变更内容，确定需要更新的相关文档
- 增量更新：只更新受影响的部分，保留用户的手动修改
- 变更确认：提供变更预览，用户确认后执行更新

**验收标准**：
- 监控`.spec/`目录下所有文档的变更
- 自动识别文档间的依赖关系
- 提供变更预览和确认机制
- 支持批量更新和回滚操作
- 保留变更历史和审计日志

### 2.2 辅助功能（可选实现）

#### 2.2.1 代码生成辅助
**功能描述**：基于规约生成代码骨架

**实现方式**：
- 命令行工具：`mcp-spec codegen [task-id]`
- 根据设计规约生成基础代码结构
- 支持常见框架的代码模板

#### 2.2.2 Git集成
**功能描述**：与版本控制系统集成

**实现方式**：
- Git hooks集成
- 提交时自动检查规约完整性
- 分支切换时同步规约状态

## 3. 技术架构设计

### 3.1 整体架构
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  AI开发工具     │    │   MCP服务器     │    │   CLI工具       │
│                 │    │                 │    │                 │
│ - Augment       │◄──►│ - 规约生成      │◄──►│ - 命令解析      │
│ - Cursor        │    │ - 模板管理      │    │ - 文件管理      │
│ - VS Code       │    │ - 上下文管理    │    │ - 状态跟踪      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   AI模型推理    │    │   规约模板      │    │   本地文件      │
│                 │    │                 │    │                 │
│ - Claude/GPT    │    │ - requirements  │    │ - .spec/        │
│ - 本地模型      │    │ - design        │    │ - .mcp/         │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 3.2 技术栈选择

#### 核心技术
- **开发语言**：Python 3.12+（跨平台，丰富的AI库支持）
- **CLI框架**：Click（简单易用的命令行框架）
- **MCP实现**：基于官方MCP Python SDK
- **AI集成**：通过MCP协议连接AI开发工具（Augment、Cursor等）

#### 依赖库
```python
# requirements.txt
click>=8.0.0
mcp>=0.1.0
openai>=1.0.0
anthropic>=0.3.0
pydantic>=2.0.0
jinja2>=3.0.0
gitpython>=3.1.0
rich>=13.0.0  # 美化命令行输出
```

### 3.3 文件结构
```
mcp-spec-tool/
├── src/
│   ├── mcp_spec/
│   │   ├── __init__.py
│   │   ├── cli.py           # 命令行入口
│   │   ├── core/
│   │   │   ├── generator.py # 规约生成核心
│   │   │   ├── parser.py    # 需求解析
│   │   │   └── tracker.py   # 任务跟踪
│   │   ├── mcp/
│   │   │   ├── server.py    # MCP服务器
│   │   │   └── handlers.py  # MCP处理器
│   │   ├── templates/       # 规约模板
│   │   └── utils/
│   └── tests/
├── templates/               # 项目模板
├── docs/
├── setup.py
└── README.md
```

## 4. MCP集成方案

### 4.1 与AI开发工具的集成

#### 4.1.1 支持的AI开发工具
- **Augment Code**：通过MCP服务器提供规约生成能力
- **Cursor**：集成到Cursor的AI工作流中
- **VS Code + Continue**：作为MCP工具在VS Code中使用
- **其他支持MCP的工具**：可扩展支持

#### 4.1.2 MCP服务器配置
```json
{
  "mcpServers": {
    "spec-generator": {
      "command": "python",
      "args": ["-m", "mcp_spec.server"],
      "env": {
        "SPEC_CONFIG_PATH": "./.spec/config.json"
      }
    }
  }
}
```

#### 4.1.3 在Augment中的使用流程
1. **安装MCP工具**：`pip install mcp-spec-tool`
2. **配置MCP服务器**：在Augment设置中添加MCP服务器配置
3. **初始化项目**：在项目中运行 `mcp-spec init`
4. **在Augment中使用**：
   ```
   @spec-generator 请为"用户认证功能"生成完整规约
   ```
5. **自动生成文档**：AI工具调用MCP服务器生成规约文档

#### 4.1.4 MCP工具定义
```python
# 在MCP服务器中定义的工具
TOOLS = [
    {
        "name": "generate_spec",
        "description": "生成功能规约文档",
        "inputSchema": {
            "type": "object",
            "properties": {
                "intent": {"type": "string", "description": "功能需求描述"},
                "mode": {"type": "string", "enum": ["quick", "full"], "description": "生成模式"}
            },
            "required": ["intent"]
        }
    },
    {
        "name": "list_tasks",
        "description": "列出当前项目的任务",
        "inputSchema": {"type": "object", "properties": {}}
    },
    {
        "name": "update_task_status",
        "description": "更新任务状态",
        "inputSchema": {
            "type": "object",
            "properties": {
                "task_id": {"type": "string"},
                "status": {"type": "string", "enum": ["todo", "doing", "done"]}
            },
            "required": ["task_id", "status"]
        }
    },
    {
        "name": "sync_documents",
        "description": "同步更新相关规约文档",
        "inputSchema": {
            "type": "object",
            "properties": {
                "changed_file": {"type": "string", "description": "发生变更的文件路径"},
                "change_type": {"type": "string", "enum": ["modified", "created", "deleted"]},
                "preview_only": {"type": "boolean", "default": false, "description": "仅预览变更，不实际执行"}
            },
            "required": ["changed_file", "change_type"]
        }
    },
    {
        "name": "watch_documents",
        "description": "启动文档监控",
        "inputSchema": {
            "type": "object",
            "properties": {
                "watch_path": {"type": "string", "default": ".spec", "description": "监控的目录路径"},
                "auto_update": {"type": "boolean", "default": false, "description": "是否自动更新"}
            }
        }
    }
]
```

## 5. 核心功能实现细节

### 5.1 MCP服务器实现

#### 5.1.1 服务器配置
```python
# mcp/server.py
from mcp import Server
from mcp.types import Tool, TextContent

class SpecMCPServer:
    def __init__(self):
        self.server = Server("spec-generator")
        self.setup_tools()
    
    def setup_tools(self):
        @self.server.tool()
        async def generate_spec(intent: str, context: dict) -> str:
            """生成项目规约"""
            return await self.generate_specification(intent, context)

        @self.server.tool()
        async def parse_requirements(requirements: str) -> dict:
            """解析需求文档"""
            return await self.parse_requirement_doc(requirements)

        @self.server.tool()
        async def sync_documents(changed_file: str, change_type: str, preview_only: bool = False) -> str:
            """同步更新相关规约文档"""
            return await self.sync_related_documents(changed_file, change_type, preview_only)

        @self.server.tool()
        async def watch_documents(watch_path: str = ".spec", auto_update: bool = False) -> str:
            """启动文档监控"""
            return await self.start_document_watcher(watch_path, auto_update)
```

#### 5.1.2 规约生成逻辑
```python
# core/generator.py
class SpecGenerator:
    def __init__(self, ai_client):
        self.ai_client = ai_client
        self.templates = self.load_templates()
    
    async def generate_requirements(self, intent: str, project_context: dict):
        """生成需求规约"""
        prompt = self.build_requirements_prompt(intent, project_context)
        response = await self.ai_client.generate(prompt)
        return self.format_requirements(response)
    
    async def generate_design(self, requirements: str, project_context: dict):
        """生成设计规约"""
        prompt = self.build_design_prompt(requirements, project_context)
        response = await self.ai_client.generate(prompt)
        return self.format_design(response)
    
    async def generate_tasks(self, design: str, requirements: str):
        """生成任务分解"""
        prompt = self.build_tasks_prompt(design, requirements)
        response = await self.ai_client.generate(prompt)
        return self.format_tasks(response)

    async def sync_related_documents(self, changed_file: str, change_type: str, preview_only: bool):
        """同步更新相关规约文档"""
        dependency_map = self.get_document_dependencies()
        affected_files = dependency_map.get(changed_file, [])

        updates = []
        for file_path in affected_files:
            if change_type == "modified":
                update = await self.generate_incremental_update(changed_file, file_path)
                updates.append({"file": file_path, "update": update})

        if preview_only:
            return self.format_update_preview(updates)
        else:
            return await self.apply_updates(updates)

    def get_document_dependencies(self):
        """获取文档依赖关系映射"""
        return {
            ".spec/project.md": [".spec/architecture.md", ".spec/conventions.md"],
            ".spec/architecture.md": ["features/*/design.md"],
            ".spec/conventions.md": ["features/*/design.md", "features/*/tasks.md"],
            "features/*/requirements.md": ["features/*/design.md", "features/*/tasks.md"],
            "features/*/design.md": ["features/*/tasks.md"]
        }
```

### 5.2 命令行接口设计

#### 5.2.1 主要命令
```python
# cli.py
import click

@click.group()
def cli():
    """MCP规约驱动开发工具"""
    pass

@cli.command()
@click.argument('project_name')
@click.option('--template', '-t', default='basic', help='项目模板')
def init(project_name, template):
    """初始化新项目"""
    click.echo(f"初始化项目: {project_name}")
    # 实现项目初始化逻辑

@cli.command()
@click.argument('intent')
@click.option('--mode', '-m', default='full', help='生成模式: quick/full')
def generate(intent, mode):
    """生成功能规约"""
    click.echo(f"生成规约: {intent}")
    # 实现规约生成逻辑

@cli.command()
def status():
    """显示项目状态"""
    # 显示任务进度和项目状态

@cli.command()
@click.argument('task_id', required=False)
@click.option('--complete', '-c', is_flag=True, help='标记任务完成')
def task(task_id, complete):
    """任务管理"""
    # 实现任务管理逻辑

@cli.command()
@click.option('--file', '-f', help='指定要同步的文件')
@click.option('--preview', '-p', is_flag=True, help='仅预览变更')
@click.option('--all', '-a', is_flag=True, help='同步所有文档')
def sync(file, preview, all):
    """同步更新相关规约文档"""
    # 实现文档同步逻辑

@cli.command()
@click.option('--path', default='.spec', help='监控的目录路径')
@click.option('--auto-update', is_flag=True, help='自动更新')
def watch(path, auto_update):
    """启动文档监控"""
    # 实现文档监控逻辑
```

#### 5.2.2 使用示例

**命令行使用：**
```bash
# 初始化项目
mcp-spec init my-project --template react

# 生成功能规约
mcp-spec generate "添加用户注册和登录功能"

# 查看项目状态
mcp-spec status

# 管理任务
mcp-spec task list
mcp-spec task 1 --complete

# 文档同步
mcp-spec sync --file .spec/project.md --preview  # 预览变更
mcp-spec sync --file .spec/architecture.md       # 执行同步
mcp-spec watch --auto-update                     # 启动自动监控
```

**在Augment中使用：**
```
# 1. 初始化项目规约
@spec-generator 请初始化一个React项目的规约框架

# 2. 生成功能规约
@spec-generator 请为"用户认证系统"生成完整的规约文档，包括注册、登录、密码重置功能

# 3. 查看任务列表
@spec-generator 显示当前项目的所有任务

# 4. 更新任务状态
@spec-generator 将任务"实现用户注册API"标记为已完成

# 5. 生成代码骨架
@spec-generator 基于设计规约生成用户认证相关的代码结构

# 6. 启动文档监控
@spec-generator 启动文档监控，当项目宪法更新时自动同步相关规约

# 7. 手动同步文档
@spec-generator 我刚刚更新了architecture.md，请同步更新所有相关的功能设计文档

# 8. 预览文档变更
@spec-generator 预览更新conventions.md后会影响哪些文档，不要实际执行更新
```

### 5.3 配置管理

#### 5.3.1 配置文件结构
```json
{
  "project": {
    "name": "my-project",
    "description": "项目描述",
    "tech_stack": ["React", "Node.js", "MongoDB"],
    "created_at": "2025-01-29"
  },
  "mcp": {
    "server_name": "spec-generator",
    "tools": ["generate_spec", "list_tasks", "update_task_status", "sync_documents", "watch_documents"],
    "auto_start": true
  },
  "ai": {
    "provider": "mcp",
    "fallback_provider": "claude",
    "model": "claude-3-sonnet",
    "max_tokens": 4000
  },
  "templates": {
    "requirements": "templates/requirements.md.j2",
    "design": "templates/design.md.j2",
    "tasks": "templates/tasks.md.j2"
  },
  "preferences": {
    "language": "zh-CN",
    "output_format": "markdown",
    "auto_save": true,
    "mcp_integration": true
  },
  "document_sync": {
    "auto_watch": true,
    "auto_update": false,
    "backup_before_update": true,
    "update_delay_seconds": 2,
    "excluded_files": [".spec/temp/*", "*.tmp"]
  }
}
```

## 6. 文档自动更新技术实现

### 6.1 文档监控机制

#### 6.1.1 文件系统监控
```python
# core/watcher.py
import asyncio
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class DocumentWatcher(FileSystemEventHandler):
    def __init__(self, sync_handler):
        self.sync_handler = sync_handler
        self.debounce_delay = 2  # 防抖延迟
        self.pending_updates = {}

    def on_modified(self, event):
        if not event.is_directory and event.src_path.endswith('.md'):
            self.schedule_sync(event.src_path, 'modified')

    def schedule_sync(self, file_path, change_type):
        # 防抖处理，避免频繁更新
        if file_path in self.pending_updates:
            self.pending_updates[file_path].cancel()

        self.pending_updates[file_path] = asyncio.create_task(
            self.delayed_sync(file_path, change_type)
        )

    async def delayed_sync(self, file_path, change_type):
        await asyncio.sleep(self.debounce_delay)
        await self.sync_handler.sync_documents(file_path, change_type)
```

### 6.2 依赖关系分析
```python
# core/dependency.py
class DocumentDependencyAnalyzer:
    def get_affected_documents(self, changed_file):
        """获取受影响的文档列表"""
        dependency_map = {
            ".spec/project.md": ["features/*/requirements.md"],
            ".spec/architecture.md": ["features/*/design.md"],
            ".spec/conventions.md": ["features/*/design.md", "features/*/tasks.md"],
            "features/*/requirements.md": ["features/*/design.md"],
            "features/*/design.md": ["features/*/tasks.md"]
        }

        affected = []
        for pattern, dependencies in dependency_map.items():
            if self.matches_pattern(changed_file, pattern):
                affected.extend(dependencies)
        return self.resolve_patterns(affected)
```

## 7. MCP集成的优势

### 7.1 对个人开发者的价值
- **零成本AI使用**：利用已有的AI开发工具，无需额外的API费用
- **无缝集成体验**：在熟悉的开发环境中直接使用规约生成功能
- **上下文感知**：AI工具已经理解项目结构和代码上下文
- **持续可用**：不依赖外部API服务的稳定性

### 7.2 技术优势
- **标准化协议**：基于MCP标准，确保与多种AI工具的兼容性
- **轻量级实现**：MCP服务器资源占用小，启动快速
- **扩展性强**：可以轻松添加新的工具和功能
- **本地优先**：敏感代码不需要发送到外部服务

### 7.3 与直接API调用的对比

| 特性 | MCP集成方式 | 直接API调用 |
|------|-------------|-------------|
| 成本 | 免费（利用现有工具） | 按使用量付费 |
| 隐私 | 高（本地处理） | 中（数据传输到云端） |
| 集成复杂度 | 低（标准协议） | 中（需要处理多种API） |
| 上下文感知 | 强（AI工具提供） | 弱（需要手动提供） |
| 离线使用 | 支持（本地模型） | 不支持 |
| 维护成本 | 低 | 中（需要管理API密钥等） |

## 8. 开发计划

### 8.1 MVP开发阶段（4-6周）

#### 第1周：基础架构
- [ ] 项目结构搭建
- [ ] CLI框架集成
- [ ] MCP服务器基础实现
- [ ] 配置管理系统

#### 第2周：核心功能
- [ ] 项目初始化功能
- [ ] MCP服务器集成（支持Augment等AI工具）
- [ ] 基础规约生成
- [ ] 模板系统

#### 第3周：规约生成
- [ ] 需求规约生成
- [ ] 设计规约生成
- [ ] 任务分解功能
- [ ] 文档格式化

#### 第4周：任务管理和文档同步
- [ ] 任务状态跟踪
- [ ] 进度统计
- [ ] 状态持久化
- [ ] 文档监控机制
- [ ] 文档依赖关系分析
- [ ] 增量更新算法

#### 第5-6周：测试和优化
- [ ] 单元测试编写
- [ ] 集成测试
- [ ] 文档同步功能测试
- [ ] 性能优化
- [ ] 命令行美化
- [ ] 用户文档完善

### 8.2 发布计划
- **Alpha版本**：核心功能可用，内部测试
- **Beta版本**：功能完整，开放测试
- **正式版本**：稳定发布，支持多平台

## 9. 成本估算

### 9.1 开发成本
- **时间投入**：4-6周全职开发
- **AI服务费用**：$0（利用现有AI开发工具）
- **工具和服务**：$20/月（GitHub、域名等）

### 9.2 运营成本
- **AI费用**：$0（通过MCP使用现有工具）
- **服务器费用**：$0（本地工具，无需服务器）
- **维护时间**：每周1-2小时

## 10. 风险评估

### 10.1 技术风险
| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| AI模型不稳定 | 中 | 中 | 支持多个AI提供商 |
| MCP协议变更 | 低 | 低 | 关注官方更新 |
| 性能问题 | 低 | 低 | 本地优先设计 |

### 10.2 市场风险
| 风险 | 影响 | 概率 | 缓解措施 |
|------|------|------|----------|
| 用户接受度低 | 中 | 中 | 简化使用流程 |
| 竞品出现 | 中 | 高 | 专注个人开发者 |
| 需求不明确 | 高 | 中 | 快速迭代验证 |

## 11. 成功指标

### 11.1 产品指标
- **安装量**：第一个月 > 100次下载
- **活跃用户**：周活跃用户 > 20人
- **功能使用率**：核心功能使用率 > 70%

### 11.2 用户反馈
- **用户满意度**：4.0/5.0以上
- **功能完成度**：用户认为功能基本满足需求
- **推荐意愿**：50%以上用户愿意推荐

### 11.3 技术指标
- **响应时间**：规约生成 < 30秒
- **成功率**：规约生成成功率 > 90%
- **稳定性**：无严重bug，崩溃率 < 1%

## 12. 后续发展方向

### 12.1 功能扩展
- VS Code插件开发
- 更多项目模板支持
- 团队协作功能
- 代码生成增强
- 智能冲突解决机制
- 文档版本控制集成
- 实时协作编辑支持

### 12.2 商业化考虑
- 免费版本：基础功能
- 付费版本：高级功能、更多AI模型
- 企业版本：团队功能、私有部署

---

**文档版本**：v1.0  
**创建日期**：2025-01-29  
**目标用户**：个人开发者、SOLO founder  
**开发周期**：4-6周  
**预算范围**：$100-200
