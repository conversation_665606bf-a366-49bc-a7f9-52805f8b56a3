你是一位经验丰富的首席技术官（CTO）和资深系统架构师，你的任务是创建一个“主控提示（Master Control Prompt, MCP）”模板。这个MCP旨在根据具体的项目上下文，系统性地生成一套全面、专业且可执行的开发约束和规范文档。当用户填写完这个模板后，你将依据这些信息，生成一份高质量的开发约束文档。 

 \### 主控提示模板 (Master Control Prompt Template) 

 **核心指令：** 

 请基于以下提供的项目上下文信息，为我生成一份详细的项目开发约束文档。文档应采用Markdown格式，结构清晰，包含但不限于编码规范、架构原则、API设计、数据库规范、安全性要求、测试策略、代码版本管理、CI/CD流程和文档规范等核心部分。请确保每一项约束都具体、可衡量且具有操作性，并根据项目的特点和团队情况提供最佳实践建议。 

 \--- 

 **第一部分：项目上下文 [请用户在此处填写]** 



1. **项目概述** 

   \*  **项目名称与核心价值：** `[例如：AlphaCRM系统，一个旨在通过数据智能提升销售效率的企业级客户关系管理平台]` 

   \*  **业务领域：** `[例如：金融科技、电子商务、医疗健康、物联网、企业服务SaaS]` 

   \*  **项目阶段：** `[例如：从0到1启动阶段、快速迭代与增长阶段、成熟期维护与重构阶段]` 



2. **技术栈与环境** 

   \*  **前端技术栈：** `[例如：React 18, TypeScript, Vite, Tailwind CSS, Zustand]` 

   \*  **后端技术栈：** `[例如：Java 17, Spring Boot 3, Maven, gRPC]` 

   \*  **移动端技术栈（如适用）：** `[例如：Kotlin (Android), Swift (iOS), Flutter]` 

   \*  **数据库与缓存：** `[例如：PostgreSQL 15, Redis 7, Elasticsearch]` 

   \*  **基础设施与部署环境：** `[例如：AWS (EKS, S3, RDS), Docker, Kubernetes, Terraform, Jenkins]` 



3. **团队情况** 

   \*  **团队规模与构成：** `[例如：15人团队，包括5名后端、3名前端、2名移动端、2名测试、1名运维、1名产品经理、1名项目经理]` 

   \*  **团队经验水平：** `[例如：混合型，以中级工程师为主，有少量初级和资深工程师]` 



4. **关键非功能性需求 (NFRs)** 

   \*  **性能指标：** `[例如：核心API平均响应时间 < 50ms，99线 < 200ms；系统支持5000并发用户]` 

   \*  **安全性要求：** `[例如：必须遵循OWASP Top 10，所有敏感数据必须加密存储和传输，需通过第三方安全审计]` 

   \*  **可扩展性与可维护性：** `[例如：采用微服务架构，要求服务间低耦合，关键业务模块需支持水平扩展]` 

   \*  **合规性要求：** `[例如：需符合GDPR数据隐私法规，或国内的个人信息保护法]` 



 **第二部分：输出定制化要求 [请用户在此处选择或填写]** 



1. **文档语言：** `[中文 | 英文]` 
2. **详细程度：** `[高级别原则性指导 | 包含代码示例的详细规则 | 两者结合]` 
3. **约束的强制性级别：** `[建议性 (Should) | 强制性 (Must) | 混合说明]` 
4. **特别关注的领域：** `[请在此处列出你最希望详细阐述的1-3个领域，例如：API版本管理策略、数据库大表设计规范、前端组件化标准]`