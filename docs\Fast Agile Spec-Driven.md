引入“Fast”这个概念，意味着我们要对之前设计的“主控提示协议（MCP）”进行一次敏捷化、轻量化的改造，以解决其潜在的“流程僵化、影响迭代速度”的缺点。

我们将这个新设计称为**“FASTMCP：敏捷规约驱动协议 (Fast Agile Spec-Driven Protocol)”**。

它的核心思想是：在不牺牲核心治理能力的前提下，赋予开发者根据任务复杂度选择不同级别“规约仪式感”的权力，并引入**“代码优先、规约后置”**的同步机制，使其更符合现代敏捷开发的“心流”。

### FASTMCP 的四大核心设计原则

1. **分级规约（Tiered Specification）**：不是所有任务都需要完整的“需求-设计-任务”三件套。为不同规模的任务提供不同重量的规约流程。
2. **即时意图（Just-in-Time Intent）**：允许开发者通过更直接、更轻量级的指令快速完成小型任务和代码重构，绕过完整的规约生成。
3. **代码即规约（Code as Spec）**：承认在敏捷开发中，代码本身是最终的“真理之源”。工具必须能从代码变更中反向生成或更新规约。
4. **对话式交互（Conversational Flow）**：减少僵硬的“批准/拒绝”步骤，更多地采用流畅的、可随时纠偏的对话式流程，更接近Cursor的交互哲学。

### 基于FASTMCP的设计与实现

我们将对之前设计的AI工具进行改造，引入**“多模态指令系统”**，让开发者可以选择不同的工作模式。

#### **模式一：“闪电模式 (Lightning Mode)” - 应对高频、小颗粒度任务**

此模式用于快速的代码修改、重构、添加注释、修复bug等。

- **设计**：

  - 开发者使用一个特殊的指令前缀（如 `!` 或 `/light`）来触发此模式。
  - 此模式**完全跳过**所有 `.md` 规约文件的生成。

- **实现**：

  1. **用户输入**：在聊天框中直接对选中的代码块或文件下达指令，例如：`! 将这个函数重构为异步，并处理异常情况`。

  2. **MCP调用**：

     - 工具将这个简单指令和相关的代码上下文打包成一个**单一、自包含的MCP**。

     - 调用一个专门的**“重构代理 (Refactoring Agent)”**。

     - **代理内部提示**：

       ```
       SYSTEM PROMPT:
       你是一个代码优化专家。请直接修改用户提供的以下代码：[注入代码上下文]。用户的指令是："[将这个函数重构为异步，并处理异常情况]"。直接提供修改后的代码，无需任何解释或文档。
       ```

  3. **输出**：工具直接在`diff`对比视图中展示建议的代码修改，供用户一键接受。

#### **模式二：“敏捷模式 (Agile Mode)” - 应对中等复杂度的特性**

此模式用于新功能点或API的开发，它采用简化的、可选的规约流程。

- **设计**：

  - 开发者使用标准指令 `/spec`，但可以附加标志来定制流程，如 `--tasks-only` 或 `--no-reqs`。
  - 目标是快速生成可执行的计划，而不必拘泥于完整的文档。

- **实现**：

  1. **用户输入**：`/spec --tasks-only "创建一个GET /api/v1/users/{id} 的端点"`。

  2. **MCP调用**：

     - 工具理解`--tasks-only`标志，并启动一个**简化的MCP链**。

     - 它会跳过“产品经理代理”和“设计师代理”，直接调用**“规划师代理”**。

     - **代理内部提示**：

       ```
       SYSTEM PROMPT:
       你是一位高级工程师。请为任务 "[创建一个GET /api/v1/users/{id} 的端点]" 生成一份直接的、可执行的 `tasks.md` 清单。请参考项目的编码规范 [注入 conventions.md]。
       ```

  3. **输出**：直接在“Task Runner”视图中生成任务列表，让开发者可以立即开始交互式编码。

#### **模式三：“全规约模式 (Full Spec Mode)” - 应对大型、关键性模块**

这保留了我们最初设计的完整流程，用于需要深思熟虑和团队评审的复杂功能。

- **设计**：
  - 使用一个明确的指令如 `/spec-full` 来触发。
- **实现**：
  1. **用户输入**：`/spec-full "设计并实现一个支持多种支付渠道的支付网关模块"`。
  2. **MCP调用**：
     - 工具严格执行我们之前设计的**“需求-设计-任务”三段式MCP链**。
     - 依次调用“产品经理代理”、“设计师代理”和“规划师代理”，每一步都需要用户明确批准。
  3. **输出**：生成完整的 `requirements.md`, `design.md`, `tasks.md` 三件套。

#### **FASTMCP的核心创新：反向同步 - “代码生成规约”**

这是解决“规约漂移” 和适应敏捷开发的关键功能。

- **设计**：

  - 提供一个`Spec: Sync from Code`命令。当开发者（尤其是在“闪电模式”下）直接修改代码后，可以用此命令让规约“追赶”代码。

- **实现**：

  1. **用户输入**：开发者手动修改了几个文件后，在文件树上右键点击，选择`Spec: Sync from Code`。

  2. **MCP调用**：

     - 工具自动计算出最近的代码变更（`git diff`）。

     - 调用一个专门的**“逆向工程代理 (Reverse-Engineering Agent)”**。

     - **代理内部提示**：

       ```
       SYSTEM PROMPT:
       你是一位资深技术分析师。以下是最近的代码变更（diff）：[注入 diff 内容]。请分析这些变更，并更新相关的 `.spec/` 文件（requirements.md, design.md, tasks.md），使规约文档与当前代码的实现保持完全一致。如果原始规约不存在，请根据代码变更创建新的规约。
       ```

  3. **输出**：工具会以`diff`形式展示对`.spec/`规约文件的建议修改，让开发者确认。这真正实现了文档的“活性”。

### 总结对比

| 特性维度       | 严格MCP (Strict MCP)                 | FASTMCP                                            |
| -------------- | ------------------------------------ | -------------------------------------------------- |
| **核心哲学**   | 生产就绪优先，结构化规划             | 敏捷优先，速度与结构并重                           |
| **工作流**     | 固定的“规约 -> 代码”单向流           | 灵活的双向流（规约->代码 & 代码->规约）            |
| **规约**       | 强制、完整的三件套                   | 分级、可选、即时生成                               |
| **交互方式**   | 步骤化、基于批准的流程               | 对话式、可随时中断/切换模式                        |
| **解决的问题** | 解决“氛围编码”导致的技术债务         | 在解决技术债务的同时，缓解流程僵化和“规约漂移”问题 |
| **适用场景**   | 大型、高风险、需要强治理的企业级项目 | 所有项目，尤其是需要快速迭代的初创和敏捷团队       |